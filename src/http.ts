#!/usr/bin/env node

import express from "express";
import { config } from "dotenv";
import { resolve } from "path";
import { randomUUID } from "node:crypto";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { isInitializeRequest } from "@modelcontextprotocol/sdk/types.js";
import { registerTools } from "./tools/index.js";
import { debug } from "./lib/debug.js";

// 加载 .env
config({ path: resolve(process.cwd(), ".env") });

// 创建 MCP Server 实例
const server = new McpServer({
  name: "shell-executor",
  version: "1.0.0"
});

// 注册工具
registerTools(server);

// 存储传输层实例的映射
const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

// 启动 HTTP 服务
async function main() {
  debug.info("Starting MCP HTTP server...");

  const app = express();
  app.use(express.json());

  // MCP 的入口 HTTP POST
  app.post("/mcp", async (req, res) => {
    try {
      const sessionId = req.headers["mcp-session-id"] as string | undefined;
      let transport: StreamableHTTPServerTransport;

      if (sessionId && transports[sessionId]) {
        // 重用现有的传输层
        debug.info("Reusing existing transport for sessionId", sessionId);
        transport = transports[sessionId];
      } else if (!sessionId && isInitializeRequest(req.body)) {
        // 创建新的传输层用于初始化请求
        debug.info("Creating new transport for initialization request");
        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => randomUUID(),
          onsessioninitialized: (sessionId) => {
            // 存储传输层实例
            transports[sessionId] = transport;
            debug.info("Session initialized with ID:", sessionId);
          },
        });

        transport.onclose = () => {
          if (transport.sessionId) {
            delete transports[transport.sessionId];
            debug.info("Transport closed for session:", transport.sessionId);
          }
        };

        // 连接服务器到传输层
        await server.connect(transport);
      } else {
        // 无效请求
        res.status(400).json({
          jsonrpc: "2.0",
          error: {
            code: -32600,
            message: "Invalid request - missing session ID or not an initialize request",
          },
          id: null,
        });
        return;
      }

      // 处理请求
      await transport.handleRequest(req, res, req.body);
    } catch (err) {
      debug.error("MCP handle error", err);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: "2.0",
          error: {
            code: -32603,
            message: "Internal server error",
          },
          id: null,
        });
      }
    }
  });

  // 处理 GET 和 DELETE 请求（用于会话管理）
  const handleSessionRequest = async (req: express.Request, res: express.Response) => {
    const sessionId = req.headers["mcp-session-id"] as string | undefined;
    if (!sessionId || !transports[sessionId]) {
      res.status(400).send("Invalid or missing session ID");
      return;
    }

    debug.info(`Received session request for session ${sessionId}`);

    try {
      const transport = transports[sessionId];
      await transport.handleRequest(req, res);
    } catch (error) {
      debug.error("Error handling session request:", error);
      if (!res.headersSent) {
        res.status(500).send("Error processing session request");
      }
    }
  };

  // 处理 GET 请求（用于服务器到客户端的通知）
  app.get("/mcp", handleSessionRequest);

  // 处理 DELETE 请求（用于会话终止）
  app.delete("/mcp", handleSessionRequest);

  const port = process.env.PORT || 3000;
  app.listen(port, () => {
    debug.info(`MCP HTTP Server is running at http://localhost:${port}`);
  });
}

// 错误处理
process.on('uncaughtException', (error) => {
  debug.error("Uncaught Exception", error);
  process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
  debug.error("Unhandled Rejection", { reason, promise });
  process.exit(1);
});

main().catch((error) => {
  debug.error("Failed to start server", error);
  process.exit(1);
});