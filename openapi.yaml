openapi: 3.1.1
info:
  title: MCP HTTP Server API
  description: |
    Model Context Protocol (MCP) HTTP Server API
    
    这是一个基于 MCP 协议的 HTTP 服务器，提供工具调用、系统信息获取和 Figma 集成功能。
    服务器使用 Server-Sent Events (SSE) 格式返回响应，支持会话管理和实时通信。
    
    ## 认证
    目前不需要认证，但需要正确的会话管理。
    
    ## 使用方式
    - 直接调用各种 MCP 方法，无需会话管理
    - 支持工具列表查询和工具调用
    
    ## 响应格式
    所有响应都使用 Server-Sent Events (SSE) 格式：
    ```
    event: message
    data: {"result": {...}, "jsonrpc": "2.0", "id": 1}
    ```
  version: 1.0.0
  contact:
    name: MCP HTTP Server
    url: https://github.com/your-repo/mcp-http-server
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://dd593cb9d65a.ngrok-free.app
    description: 开发服务器

paths:
  /mcp:
    post:
      summary: MCP 协议主入口
      description: |
        处理所有 MCP 协议请求，包括初始化、工具列表查询、工具调用等。
        
        ## 请求流程
        1. 首先发送 `initialize` 请求建立会话
        2. 使用返回的 session ID 进行后续请求
        3. 可以调用 `tools/list` 获取可用工具
        4. 使用 `tools/call` 执行具体工具
      operationId: mcpRequest
      tags:
        - MCP Protocol
      parameters:
        - name: mcp-session-id
          in: header
          description: 会话 ID（初始化请求后必需）
          required: false
          schema:
            type: string
            format: uuid
            example: "550e8400-e29b-41d4-a716-************"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JsonRpcRequest'
            examples:
              initialize:
                summary: 初始化连接
                value:
                  jsonrpc: "2.0"
                  id: 1
                  method: "initialize"
                  params:
                    protocolVersion: "2025-03-26"
                    capabilities:
                      tools: {}
                    clientInfo:
                      name: "test-client"
                      version: "1.0.0"
              tools_list:
                summary: 获取工具列表
                value:
                  jsonrpc: "2.0"
                  id: 2
                  method: "tools/list"
                  params: {}
              tools_call_system_info:
                summary: 调用系统信息工具
                value:
                  jsonrpc: "2.0"
                  id: 3
                  method: "tools/call"
                  params:
                    name: "get-system-info"
                    arguments: {}
              tools_call_figma_data:
                summary: 获取 Figma 数据
                value:
                  jsonrpc: "2.0"
                  id: 4
                  method: "tools/call"
                  params:
                    name: "get_figma_data"
                    arguments:
                      fileKey: "abc123def456"
                      nodeId: "1:2"
                      extractDownloadableNodes: true
              tools_call_figma_download:
                summary: 下载 Figma 图像
                value:
                  jsonrpc: "2.0"
                  id: 5
                  method: "tools/call"
                  params:
                    name: "download_figma_images"
                    arguments:
                      fileKey: "abc123def456"
                      nodes:
                        - nodeId: "1:2"
                          fileName: "icon.png"
                      localPath: "/path/to/images"
                      pngScale: 2
      responses:
        '200':
          description: 成功响应
          headers:
            mcp-session-id:
              description: 会话 ID（初始化响应中返回）
              schema:
                type: string
                format: uuid
          content:
            text/event-stream:
              schema:
                type: string
                description: SSE 格式的响应数据
              examples:
                initialize_response:
                  summary: 初始化响应
                  value: |
                    event: message
                    data: {"result":{"protocolVersion":"2025-03-26","capabilities":{"tools":{"listChanged":true}},"serverInfo":{"name":"shell-executor","version":"1.0.0"}},"jsonrpc":"2.0","id":1}
                tools_list_response:
                  summary: 工具列表响应
                  value: |
                    event: message
                    data: {"result":{"tools":[{"name":"get-system-info","title":"Get System Information","description":"Get basic system information"}]},"jsonrpc":"2.0","id":2}
        '400':
          description: 请求错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_request:
                  summary: 无效请求
                  value:
                    jsonrpc: "2.0"
                    error:
                      code: -32600
                      message: "Invalid request - missing session ID or not an initialize request"
                    id: null
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                internal_error:
                  summary: 内部错误
                  value:
                    jsonrpc: "2.0"
                    error:
                      code: -32603
                      message: "Internal server error"
                    id: null
    get:
      summary: 服务器到客户端通知流
      description: |
        建立 SSE 连接以接收服务器主动推送的通知。
        需要有效的会话 ID。
      operationId: mcpNotifications
      tags:
        - MCP Protocol
      parameters:
        - name: mcp-session-id
          in: header
          description: 有效的会话 ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: SSE 通知流
          content:
            text/event-stream:
              schema:
                type: string
                description: 持续的 SSE 事件流
        '400':
          description: 无效或缺失的会话 ID
          content:
            text/plain:
              schema:
                type: string
                example: "Invalid or missing session ID"
    delete:
      summary: 终止会话
      description: |
        终止指定的 MCP 会话，清理相关资源。
      operationId: terminateSession
      tags:
        - Session Management
      parameters:
        - name: mcp-session-id
          in: header
          description: 要终止的会话 ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 会话成功终止
        '400':
          description: 无效或缺失的会话 ID
          content:
            text/plain:
              schema:
                type: string
                example: "Invalid or missing session ID"
        '500':
          description: 处理会话终止时出错
          content:
            text/plain:
              schema:
                type: string
                example: "Error processing session request"

components:
  schemas:
    # JSON-RPC 基础结构
    JsonRpcRequest:
      type: object
      required:
        - jsonrpc
        - method
        - id
      properties:
        jsonrpc:
          type: string
          enum: ["2.0"]
          description: JSON-RPC 版本
          default: "2.0"
        method:
          type: string
          description: 调用的方法名
          enum: ["initialize", "tools/list", "tools/call"]
          example: "initialize"
        params:
          type: object
          description: 方法参数
          example: {
            "protocolVersion": "2025-03-26",
            "capabilities": {"tools": {}},
            "clientInfo": {"name": "test-client", "version": "1.0.0"}
          }
        id:
          type: integer
          description: 请求 ID
          example: 1
    
    JsonRpcResponse:
      type: object
      required:
        - jsonrpc
        - id
      properties:
        jsonrpc:
          type: string
          enum: ["2.0"]
        result:
          type: object
          description: 成功响应的结果
        error:
          $ref: '#/components/schemas/JsonRpcError'
        id:
          oneOf:
            - type: string
            - type: number
            - type: "null"
    
    JsonRpcError:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: 错误代码
        message:
          type: string
          description: 错误消息
        data:
          description: 额外的错误数据
    
    # MCP 特定请求类型
    InitializeRequest:
      allOf:
        - $ref: '#/components/schemas/JsonRpcRequest'
        - type: object
          properties:
            method:
              type: string
              enum: ["initialize"]
            params:
              type: object
              required:
                - protocolVersion
                - capabilities
                - clientInfo
              properties:
                protocolVersion:
                  type: string
                  enum: ["2025-03-26"]
                  description: MCP 协议版本
                capabilities:
                  type: object
                  properties:
                    tools:
                      type: object
                      description: 工具相关能力
                clientInfo:
                  type: object
                  required:
                    - name
                    - version
                  properties:
                    name:
                      type: string
                      description: 客户端名称
                    version:
                      type: string
                      description: 客户端版本
    
    ToolsListRequest:
      allOf:
        - $ref: '#/components/schemas/JsonRpcRequest'
        - type: object
          properties:
            method:
              type: string
              enum: ["tools/list"]
            params:
              type: object
              description: 通常为空对象
    
    ToolsCallRequest:
      allOf:
        - $ref: '#/components/schemas/JsonRpcRequest'
        - type: object
          properties:
            method:
              type: string
              enum: ["tools/call"]
            params:
              type: object
              required:
                - name
                - arguments
              properties:
                name:
                  type: string
                  description: 要调用的工具名称
                  enum:
                    - get-system-info
                    - list-allowed-commands
                    - get_figma_data
                    - download_figma_images
                arguments:
                  type: object
                  description: 工具参数（根据具体工具而定）
    
    # 工具定义
    Tool:
      type: object
      required:
        - name
        - title
        - description
        - inputSchema
      properties:
        name:
          type: string
          description: 工具名称
        title:
          type: string
          description: 工具标题
        description:
          type: string
          description: 工具描述
        inputSchema:
          type: object
          description: 输入参数的 JSON Schema
    
    # 工具参数定义
    SystemInfoArguments:
      type: object
      description: 系统信息工具参数（无参数）
      additionalProperties: false
    
    FigmaDataArguments:
      type: object
      required:
        - fileKey
      properties:
        fileKey:
          type: string
          description: Figma 文件的 key
        nodeId:
          type: string
          description: 节点 ID（可选）
        depth:
          type: number
          description: 遍历深度（可选）
        extractDownloadableNodes:
          type: boolean
          description: 是否提取可下载节点（可选）
    
    FigmaDownloadArguments:
      type: object
      required:
        - fileKey
        - nodes
        - localPath
      properties:
        fileKey:
          type: string
          description: Figma 文件的 key
        nodes:
          type: array
          items:
            type: object
            required:
              - nodeId
              - fileName
            properties:
              nodeId:
                type: string
                description: 节点 ID
              fileName:
                type: string
                description: 保存的文件名
              imageRef:
                type: string
                description: 图像引用（可选）
        localPath:
          type: string
          description: 本地保存路径
        pngScale:
          type: number
          default: 2
          description: PNG 缩放比例
        svgOptions:
          type: object
          properties:
            outlineText:
              type: boolean
              default: true
            includeId:
              type: boolean
              default: false
            simplifyStroke:
              type: boolean
              default: true
    
    # 响应类型
    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/JsonRpcResponse'
        - type: object
          properties:
            error:
              $ref: '#/components/schemas/JsonRpcError'
    
    InitializeResponse:
      allOf:
        - $ref: '#/components/schemas/JsonRpcResponse'
        - type: object
          properties:
            result:
              type: object
              required:
                - protocolVersion
                - capabilities
                - serverInfo
              properties:
                protocolVersion:
                  type: string
                  enum: ["2025-03-26"]
                capabilities:
                  type: object
                  properties:
                    tools:
                      type: object
                      properties:
                        listChanged:
                          type: boolean
                serverInfo:
                  type: object
                  required:
                    - name
                    - version
                  properties:
                    name:
                      type: string
                      example: "shell-executor"
                    version:
                      type: string
                      example: "1.0.0"
    
    ToolsListResponse:
      allOf:
        - $ref: '#/components/schemas/JsonRpcResponse'
        - type: object
          properties:
            result:
              type: object
              required:
                - tools
              properties:
                tools:
                  type: array
                  items:
                    $ref: '#/components/schemas/Tool'
    
    ToolsCallResponse:
      allOf:
        - $ref: '#/components/schemas/JsonRpcResponse'
        - type: object
          properties:
            result:
              type: object
              required:
                - content
              properties:
                content:
                  type: array
                  items:
                    type: object
                    required:
                      - type
                      - text
                    properties:
                      type:
                        type: string
                        enum: ["text"]
                      text:
                        type: string
                        description: 工具执行结果的文本内容

tags:
  - name: MCP Protocol
    description: Model Context Protocol 相关操作
  - name: Session Management
    description: 会话管理操作

externalDocs:
  description: MCP 协议规范
  url: https://spec.modelcontextprotocol.io/
