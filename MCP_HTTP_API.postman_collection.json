{"info": {"name": "MCP HTTP Server API", "description": "Model Context Protocol HTTP Server API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "sessionId", "value": "", "type": "string"}], "item": [{"name": "1. Initialize Connection", "event": [{"listen": "test", "script": {"exec": ["// Extract session ID from response headers", "const sessionId = pm.response.headers.get('mcp-session-id');", "if (sessionId) {", "    pm.collectionVariables.set('sessionId', sessionId);", "    console.log('Session ID saved:', sessionId);", "}", "", "// Test response", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains session ID', function () {", "    pm.expect(sessionId).to.not.be.undefined;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json, text/event-stream"}], "body": {"mode": "raw", "raw": "{\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"initialize\",\n  \"params\": {\n    \"protocolVersion\": \"2025-03-26\",\n    \"capabilities\": {\n      \"tools\": {}\n    },\n    \"clientInfo\": {\n      \"name\": \"postman-client\",\n      \"version\": \"1.0.0\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Initialize MCP connection and get session ID"}, "response": []}, {"name": "2. List Available Tools", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains tools list', function () {", "    const responseText = pm.response.text();", "    pm.expect(responseText).to.include('tools');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json, text/event-stream"}, {"key": "mcp-session-id", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"jsonrpc\": \"2.0\",\n  \"id\": 2,\n  \"method\": \"tools/list\",\n  \"params\": {}\n}"}, "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Get list of available tools"}, "response": []}, {"name": "3. Call System Info Tool", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains system info', function () {", "    const responseText = pm.response.text();", "    pm.expect(responseText).to.include('System Information');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json, text/event-stream"}, {"key": "mcp-session-id", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"jsonrpc\": \"2.0\",\n  \"id\": 3,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"get-system-info\",\n    \"arguments\": {}\n  }\n}"}, "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Call the system information tool"}, "response": []}, {"name": "4. List Allowed Commands", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json, text/event-stream"}, {"key": "mcp-session-id", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"jsonrpc\": \"2.0\",\n  \"id\": 4,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"list-allowed-commands\",\n    \"arguments\": {}\n  }\n}"}, "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Get list of allowed and forbidden commands"}, "response": []}, {"name": "5. Get Figma Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json, text/event-stream"}, {"key": "mcp-session-id", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"jsonrpc\": \"2.0\",\n  \"id\": 5,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"get_figma_data\",\n    \"arguments\": {\n      \"fileKey\": \"your-figma-file-key\",\n      \"extractDownloadableNodes\": true\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Get Figma file data (requires valid Figma file key)"}, "response": []}, {"name": "6. Download Figma Images", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json, text/event-stream"}, {"key": "mcp-session-id", "value": "{{sessionId}}"}], "body": {"mode": "raw", "raw": "{\n  \"jsonrpc\": \"2.0\",\n  \"id\": 6,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"download_figma_images\",\n    \"arguments\": {\n      \"fileKey\": \"your-figma-file-key\",\n      \"nodes\": [\n        {\n          \"nodeId\": \"1:2\",\n          \"fileName\": \"icon.png\"\n        }\n      ],\n      \"localPath\": \"/tmp/figma-images\",\n      \"pngScale\": 2\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Download images from Figma (requires valid file key and node IDs)"}, "response": []}, {"name": "7. Get Server Notifications", "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/event-stream"}, {"key": "mcp-session-id", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Establish SSE connection for server notifications"}, "response": []}, {"name": "8. Terminate Session", "request": {"method": "DELETE", "header": [{"key": "mcp-session-id", "value": "{{sessionId}}"}], "url": {"raw": "{{baseUrl}}/mcp", "host": ["{{baseUrl}}"], "path": ["mcp"]}, "description": "Terminate the MCP session"}, "response": []}]}