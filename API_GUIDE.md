# MCP HTTP Server API 使用指南

## 概述

这是一个基于 Model Context Protocol (MCP) 的 HTTP 服务器，提供工具调用、系统信息获取和 Figma 集成功能。

## 快速开始

### 1. 启动服务器
```bash
npm run dev:http
```
服务器将在 `http://localhost:3000` 启动。

### 2. 初始化连接
```bash
curl -X POST http://localhost:3000/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {"tools": {}},
      "clientInfo": {"name": "test-client", "version": "1.0.0"}
    }
  }'
```

响应会包含 `mcp-session-id` 头，保存此 ID 用于后续请求。

### 3. 获取可用工具
```bash
curl -X POST http://localhost:3000/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "mcp-session-id: YOUR_SESSION_ID" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/list",
    "params": {}
  }'
```

### 4. 调用工具
```bash
curl -X POST http://localhost:3000/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "mcp-session-id: YOUR_SESSION_ID" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "get-system-info",
      "arguments": {}
    }
  }'
```

## 可用工具

### 1. get-system-info
获取系统基本信息
```json
{
  "name": "get-system-info",
  "arguments": {}
}
```

### 2. list-allowed-commands
显示允许和禁止的命令列表
```json
{
  "name": "list-allowed-commands",
  "arguments": {}
}
```

### 3. get_figma_data
获取 Figma 文件数据
```json
{
  "name": "get_figma_data",
  "arguments": {
    "fileKey": "your-figma-file-key",
    "nodeId": "1:2",
    "extractDownloadableNodes": true
  }
}
```

### 4. download_figma_images
下载 Figma 图像
```json
{
  "name": "download_figma_images",
  "arguments": {
    "fileKey": "your-figma-file-key",
    "nodes": [
      {
        "nodeId": "1:2",
        "fileName": "icon.png"
      }
    ],
    "localPath": "/path/to/save/images",
    "pngScale": 2
  }
}
```

## 响应格式

所有响应都使用 Server-Sent Events (SSE) 格式：

```
event: message
data: {"result": {...}, "jsonrpc": "2.0", "id": 1}
```

## 错误处理

### 常见错误代码
- `-32600`: 无效请求
- `-32603`: 内部服务器错误
- `-32000`: 不可接受的请求（缺少必需的 Accept 头）

### 错误响应示例
```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32600,
    "message": "Invalid request - missing session ID or not an initialize request"
  },
  "id": null
}
```

## 会话管理

### 建立会话
1. 发送 `initialize` 请求
2. 从响应头获取 `mcp-session-id`
3. 在后续请求中包含此 session ID

### 终止会话
```bash
curl -X DELETE http://localhost:3000/mcp \
  -H "mcp-session-id: YOUR_SESSION_ID"
```

### 接收通知
```bash
curl -X GET http://localhost:3000/mcp \
  -H "mcp-session-id: YOUR_SESSION_ID" \
  -H "Accept: text/event-stream"
```

## JavaScript 客户端示例

```javascript
class MCPClient {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
    this.sessionId = null;
  }

  async initialize() {
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2025-03-26',
          capabilities: { tools: {} },
          clientInfo: { name: 'js-client', version: '1.0.0' }
        }
      })
    });

    this.sessionId = response.headers.get('mcp-session-id');
    return this.parseSSEResponse(await response.text());
  }

  async listTools() {
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream',
        'mcp-session-id': this.sessionId
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/list',
        params: {}
      })
    });

    return this.parseSSEResponse(await response.text());
  }

  async callTool(name, arguments = {}) {
    const response = await fetch(`${this.baseUrl}/mcp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream',
        'mcp-session-id': this.sessionId
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'tools/call',
        params: { name, arguments }
      })
    });

    return this.parseSSEResponse(await response.text());
  }

  parseSSEResponse(text) {
    const lines = text.split('\n');
    const dataLine = lines.find(line => line.startsWith('data: '));
    if (dataLine) {
      return JSON.parse(dataLine.substring(6));
    }
    return null;
  }
}

// 使用示例
const client = new MCPClient();
await client.initialize();
const tools = await client.listTools();
const systemInfo = await client.callTool('get-system-info');
```

## 配置

在 `.env` 文件中配置：

```env
PORT=3000
DEBUG=true
NODE_ENV=development
FIGMA_API_KEY=your_figma_api_key_here
```

## 文档

- [OpenAPI 规范](./openapi.yaml) - 完整的 API 文档
- [HTTP 服务器 README](./HTTP_SERVER_README.md) - 详细的服务器说明
