<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP HTTP API 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056CC;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .session-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 MCP HTTP API 测试页面</h1>
        <p>这个页面可以帮助你测试 MCP HTTP 服务器的各种功能。</p>
        
        <div class="session-info">
            <strong>会话状态:</strong> <span id="sessionStatus">未连接</span><br>
            <strong>会话 ID:</strong> <span id="sessionId">无</span>
        </div>
    </div>

    <div class="container">
        <h2>🔧 服务器配置</h2>
        <div class="input-group">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="http://localhost:3000" placeholder="http://localhost:3000">
        </div>
    </div>

    <div class="container">
        <h2>1️⃣ 初始化连接</h2>
        <p>首先需要初始化 MCP 连接以获取会话 ID。</p>
        <button onclick="initialize()">初始化连接</button>
        <div id="initResult"></div>
    </div>

    <div class="container">
        <h2>2️⃣ 工具管理</h2>
        <p>获取可用工具列表和调用工具。</p>
        <button onclick="listTools()" id="listToolsBtn" disabled>获取工具列表</button>
        <button onclick="callSystemInfo()" id="systemInfoBtn" disabled>获取系统信息</button>
        <button onclick="listCommands()" id="listCommandsBtn" disabled>列出允许的命令</button>
        <div id="toolsResult"></div>
    </div>

    <div class="container">
        <h2>3️⃣ Figma 工具</h2>
        <p>测试 Figma 相关功能（需要有效的 Figma 文件 Key）。</p>
        <div class="input-group">
            <label for="figmaFileKey">Figma 文件 Key:</label>
            <input type="text" id="figmaFileKey" placeholder="输入 Figma 文件的 Key">
        </div>
        <button onclick="getFigmaData()" id="figmaDataBtn" disabled>获取 Figma 数据</button>
        <div id="figmaResult"></div>
    </div>

    <div class="container">
        <h2>4️⃣ 自定义请求</h2>
        <p>发送自定义的 MCP 请求。</p>
        <div class="input-group">
            <label for="customRequest">请求 JSON:</label>
            <textarea id="customRequest" placeholder='{"jsonrpc": "2.0", "id": 1, "method": "tools/list", "params": {}}'></textarea>
        </div>
        <button onclick="sendCustomRequest()" id="customBtn" disabled>发送请求</button>
        <div id="customResult"></div>
    </div>

    <div class="container">
        <h2>📋 响应日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <pre id="responseLog"></pre>
    </div>

    <script>
        let sessionId = null;
        let requestId = 1;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('responseLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateSessionStatus() {
            document.getElementById('sessionStatus').textContent = sessionId ? '已连接' : '未连接';
            document.getElementById('sessionId').textContent = sessionId || '无';
            
            // 启用/禁用按钮
            const buttons = ['listToolsBtn', 'systemInfoBtn', 'listCommandsBtn', 'figmaDataBtn', 'customBtn'];
            buttons.forEach(btnId => {
                document.getElementById(btnId).disabled = !sessionId;
            });
        }

        async function makeRequest(data) {
            const serverUrl = document.getElementById('serverUrl').value;
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            };

            if (sessionId) {
                headers['mcp-session-id'] = sessionId;
            }

            try {
                log(`发送请求: ${JSON.stringify(data)}`);
                
                const response = await fetch(`${serverUrl}/mcp`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(data)
                });

                // 获取会话 ID（如果是初始化请求）
                if (!sessionId && response.headers.get('mcp-session-id')) {
                    sessionId = response.headers.get('mcp-session-id');
                    updateSessionStatus();
                    log(`获得会话 ID: ${sessionId}`);
                }

                const responseText = await response.text();
                log(`收到响应: ${responseText}`);

                // 解析 SSE 格式的响应
                if (responseText.startsWith('event: message\ndata: ')) {
                    const jsonData = responseText.replace('event: message\ndata: ', '').trim();
                    return JSON.parse(jsonData);
                }

                return { error: '无法解析响应格式' };
            } catch (error) {
                log(`请求错误: ${error.message}`, 'error');
                return { error: error.message };
            }
        }

        async function initialize() {
            const request = {
                jsonrpc: "2.0",
                id: requestId++,
                method: "initialize",
                params: {
                    protocolVersion: "2025-03-26",
                    capabilities: { tools: {} },
                    clientInfo: { name: "web-test-client", version: "1.0.0" }
                }
            };

            const response = await makeRequest(request);
            
            if (response.result) {
                showStatus('initResult', '✅ 连接初始化成功！', 'success');
            } else {
                showStatus('initResult', `❌ 初始化失败: ${response.error?.message || '未知错误'}`, 'error');
            }
        }

        async function listTools() {
            const request = {
                jsonrpc: "2.0",
                id: requestId++,
                method: "tools/list",
                params: {}
            };

            const response = await makeRequest(request);
            
            if (response.result && response.result.tools) {
                const tools = response.result.tools;
                let toolsHtml = '<h3>可用工具:</h3><ul>';
                tools.forEach(tool => {
                    toolsHtml += `<li><strong>${tool.name}</strong>: ${tool.description}</li>`;
                });
                toolsHtml += '</ul>';
                showStatus('toolsResult', toolsHtml, 'success');
            } else {
                showStatus('toolsResult', `❌ 获取工具列表失败: ${response.error?.message || '未知错误'}`, 'error');
            }
        }

        async function callSystemInfo() {
            const request = {
                jsonrpc: "2.0",
                id: requestId++,
                method: "tools/call",
                params: {
                    name: "get-system-info",
                    arguments: {}
                }
            };

            const response = await makeRequest(request);
            
            if (response.result && response.result.content) {
                const content = response.result.content[0].text;
                showStatus('toolsResult', `<h3>系统信息:</h3><pre>${content}</pre>`, 'success');
            } else {
                showStatus('toolsResult', `❌ 获取系统信息失败: ${response.error?.message || '未知错误'}`, 'error');
            }
        }

        async function listCommands() {
            const request = {
                jsonrpc: "2.0",
                id: requestId++,
                method: "tools/call",
                params: {
                    name: "list-allowed-commands",
                    arguments: {}
                }
            };

            const response = await makeRequest(request);
            
            if (response.result && response.result.content) {
                const content = response.result.content[0].text;
                showStatus('toolsResult', `<h3>允许的命令:</h3><pre>${content}</pre>`, 'success');
            } else {
                showStatus('toolsResult', `❌ 获取命令列表失败: ${response.error?.message || '未知错误'}`, 'error');
            }
        }

        async function getFigmaData() {
            const fileKey = document.getElementById('figmaFileKey').value;
            if (!fileKey) {
                showStatus('figmaResult', '❌ 请输入 Figma 文件 Key', 'error');
                return;
            }

            const request = {
                jsonrpc: "2.0",
                id: requestId++,
                method: "tools/call",
                params: {
                    name: "get_figma_data",
                    arguments: {
                        fileKey: fileKey,
                        extractDownloadableNodes: true
                    }
                }
            };

            const response = await makeRequest(request);
            
            if (response.result) {
                showStatus('figmaResult', `<h3>Figma 数据获取成功:</h3><pre>${JSON.stringify(response.result, null, 2)}</pre>`, 'success');
            } else {
                showStatus('figmaResult', `❌ 获取 Figma 数据失败: ${response.error?.message || '未知错误'}`, 'error');
            }
        }

        async function sendCustomRequest() {
            const customRequestText = document.getElementById('customRequest').value;
            if (!customRequestText) {
                showStatus('customResult', '❌ 请输入请求 JSON', 'error');
                return;
            }

            try {
                const request = JSON.parse(customRequestText);
                const response = await makeRequest(request);
                
                showStatus('customResult', `<h3>自定义请求响应:</h3><pre>${JSON.stringify(response, null, 2)}</pre>`, 'success');
            } catch (error) {
                showStatus('customResult', `❌ JSON 解析错误: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            document.getElementById('responseLog').textContent = '';
        }

        // 初始化页面
        updateSessionStatus();
        log('页面加载完成，请先初始化连接');
    </script>
</body>
</html>
